version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: bedsharing_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: my_database
      MYSQL_USER: bedsharing
      MYSQL_PASSWORD: password
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - bedsharing_network

  adminer:
    image: adminer:latest
    container_name: bedsharing_adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - bedsharing_network

volumes:
  mysql_data:
    driver: local

networks:
  bedsharing_network:
    driver: bridge
