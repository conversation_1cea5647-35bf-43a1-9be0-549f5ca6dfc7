2025-08-07 09:06:37 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 09:06:37 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 09:06:49 | WARNING  | app.auth.jwt_handler:verify_token:77 | JWT令牌已过期
2025-08-07 09:06:49 | WARNING  | app.auth.dependencies:get_current_user:41 | 无效的认证令牌
2025-08-07 09:06:52 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 09:06:52 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 09:06:52 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 09:06:52 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 09:06:53 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:06:53 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:06:53 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:06:53 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:06:53 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:06:53 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:06:53 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:06:53 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共6条记录
2025-08-07 09:11:29 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:11:29 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:11:29 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:11:29 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:11:29 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:11:29 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:11:29 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:11:29 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共6条记录
2025-08-07 09:11:33 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年7月, 截止日期: None
2025-08-07 09:11:33 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:15:46 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:15:46 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:15:46 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:15:46 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:15:46 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:15:46 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:15:46 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:15:46 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共6条记录
2025-08-07 09:16:43 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 09:17:46 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 09:17:46 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 09:17:53 | INFO     | app.services.resident_service:create_resident:137 | 创建住户成功: 777777
2025-08-07 09:17:53 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:17:59 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 09:17:59 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 09:17:59 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 09:17:59 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 09:17:59 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:17:59 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.03秒
2025-08-07 09:17:59 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:17:59 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:17:59 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:17:59 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:17:59 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:17:59 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:24:05 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 09:24:12 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 09:24:12 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 09:24:20 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:24:20 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.04秒
2025-08-07 09:24:20 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:24:20 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:24:20 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:24:21 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:24:21 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:24:21 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:24:32 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年7月, 截止日期: None
2025-08-07 09:24:32 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:24:46 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:24:55 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:24:56 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:24:56 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:24:56 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:28:13 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:28:13 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:28:13 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:28:13 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:28:13 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:28:13 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:28:13 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:28:13 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:28:25 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:33:03 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:33:03 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:33:03 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:33:03 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:33:03 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:33:03 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:33:03 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:33:03 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:35:05 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:35:05 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:35:05 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:35:05 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:35:05 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:35:05 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:35:05 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:35:05 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:35:16 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 09:35:21 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 09:35:21 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 09:35:27 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:35:27 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.03秒
2025-08-07 09:35:27 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:35:27 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:35:27 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:35:27 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:35:27 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:35:27 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:39:36 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:39:36 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:39:36 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:39:36 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:39:36 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:39:36 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:39:36 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:39:36 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:39:52 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:41:50 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:41:50 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:41:50 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:41:50 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:41:50 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:41:50 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:41:50 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:41:50 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:41:59 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 09:42:10 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 09:42:10 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 09:42:31 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:42:31 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.03秒
2025-08-07 09:42:31 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:42:31 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:42:31 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:42:31 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:42:31 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:42:31 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:46:54 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:46:54 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:46:54 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:46:54 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:46:54 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:46:54 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:46:54 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:46:54 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:46:59 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:46:59 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:00 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:00 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:00 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:01 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:01 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:01 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:02 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 09:47:29 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 09:47:29 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 09:47:29 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 09:47:29 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 09:47:29 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 09:47:29 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 09:47:29 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 09:47:29 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:10:44 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:10:44 | ERROR    | app.main:startup_event:93 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:11:24 | WARNING  | app.auth.jwt_handler:verify_token:77 | JWT令牌已过期
2025-08-07 10:11:24 | WARNING  | app.auth.dependencies:get_current_user:41 | 无效的认证令牌
2025-08-07 10:11:24 | WARNING  | app.auth.jwt_handler:verify_token:77 | JWT令牌已过期
2025-08-07 10:11:24 | WARNING  | app.auth.dependencies:get_current_user:41 | 无效的认证令牌
2025-08-07 10:11:24 | WARNING  | app.auth.jwt_handler:verify_token:77 | JWT令牌已过期
2025-08-07 10:11:24 | WARNING  | app.auth.dependencies:get_current_user:41 | 无效的认证令牌
2025-08-07 10:13:09 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:13:19 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:13:19 | ERROR    | app.main:startup_event:93 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:14:30 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: testuser
2025-08-07 10:14:30 | INFO     | app.auth.ldap_auth:authenticate_user:34 | 模拟认证成功: testuser
2025-08-07 10:14:30 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: testuser
2025-08-07 10:14:30 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: testuser
2025-08-07 10:16:09 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:16:16 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:16:16 | ERROR    | app.main:startup_event:93 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:20 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:16:20 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:16:26 | INFO     | app.api.v1.auth:logout:109 | 用户登出: testuser
2025-08-07 10:16:30 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 10:16:30 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 10:16:30 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 10:16:30 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 10:16:30 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:16:30 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:16:40 | ERROR    | app.services.dormitory_service:get_dormitories:68 | 获取宿舍列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:40 | ERROR    | app.services.department_service:get_departments:56 | 获取部门列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:40 | ERROR    | app.services.resident_service:get_residents:69 | 获取住户列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:40 | ERROR    | app.api.v1.residents:get_residents:44 | 获取住户列表API失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:40 | ERROR    | app.services.record_service:get_records:77 | 获取入住记录列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:16:40 | ERROR    | app.api.v1.records:get_records:49 | 获取入住记录列表API失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'172.18.0.1' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:17:45 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:20:14 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:20:14 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:20:21 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:20:21 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:20:21 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:20:21 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:20:24 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:20:26 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:20:27 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:20:27 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:20:29 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:20:29 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.03秒
2025-08-07 10:20:29 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:20:29 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 10:20:39 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年7月, 截止日期: None
2025-08-07 10:20:39 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共6条记录
2025-08-07 10:20:44 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:20:44 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.00秒
2025-08-07 10:21:00 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年7月, 截止日期: None
2025-08-07 10:21:00 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 10:21:02 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年7月, 截止日期: None
2025-08-07 10:21:02 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 10:21:12 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:21:12 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:21:12 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:21:12 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:21:13 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:21:14 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:21:15 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:21:15 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:21:16 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:21:16 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 10:21:16 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:21:16 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 10:21:20 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:21:20 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:21:20 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:21:20 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:25:56 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:26:03 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:26:03 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:26:09 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:26:09 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:09 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:26:09 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:26:11 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:26:11 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.05秒
2025-08-07 10:26:11 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:26:11 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共1条记录
2025-08-07 10:26:12 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:18 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:26:20 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:20 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:26:24 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共3条记录
2025-08-07 10:26:28 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:26:30 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:31 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:32 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:26:32 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:26:32 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:26:32 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:27:02 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 111111 -> 宿舍3
2025-08-07 10:27:02 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:28:27 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:28:27 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:28:27 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:28:27 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:29:17 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:29:24 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:29:24 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:30:19 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:30:19 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:30:24 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:30:27 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:30:29 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:30:29 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:30:29 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:30:29 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:30:39 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:30:41 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:30:41 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:30:41 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:30:41 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:32:54 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:32:54 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:32:54 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:32:54 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:33:37 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:33:44 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:33:44 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:35:27 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:35:27 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:35:46 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:35:57 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:35:57 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:36:00 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:02 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:04 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:06 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:08 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:10 | WARNING  | app.auth.dependencies:get_current_user:30 | 请求缺少认证令牌
2025-08-07 10:36:29 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:36:42 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:36:42 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:36:45 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 10:36:45 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 10:36:45 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 10:36:45 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 10:36:47 | ERROR    | app.services.record_service:checkout_resident:320 | 办理住户离开失败: 离开日期不能早于入住日期
2025-08-07 10:36:49 | ERROR    | app.services.record_service:checkout_resident:320 | 办理住户离开失败: 离开日期不能早于入住日期
2025-08-07 10:37:04 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:37:11 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:37:11 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:37:31 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 10:37:31 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 10:37:31 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 10:37:31 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 10:37:39 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:37:55 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:37:55 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:37:57 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: admin
2025-08-07 10:37:57 | INFO     | app.auth.ldap_auth:authenticate_user:38 | 模拟认证成功: admin
2025-08-07 10:37:57 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: admin
2025-08-07 10:37:57 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: admin
2025-08-07 10:37:59 | INFO     | app.services.record_service:get_record:109 | 获取入住记录成功: 8444591b-6340-4902-96c3-1b2fb854026e
2025-08-07 10:38:02 | INFO     | app.services.record_service:checkout_resident:316 | 办理住户离开成功: 111111
2025-08-07 10:38:17 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:38:24 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:38:24 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:38:24 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:38:31 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:38:31 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:38:33 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:38:36 | INFO     | app.api.v1.auth:logout:109 | 用户登出: admin
2025-08-07 10:38:45 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:38:45 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:38:45 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:38:57 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:38:57 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:38:57 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:39:06 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:39:06 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:39:06 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:39:09 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:39:14 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:39:14 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:39:14 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:39:18 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:39:18 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:39:22 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:39:22 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:39:27 | INFO     | app.api.v1.auth:login:31 | 用户登录请求: testuser
2025-08-07 10:39:27 | INFO     | app.auth.ldap_auth:authenticate_user:34 | 模拟认证成功: testuser
2025-08-07 10:39:27 | INFO     | app.auth.jwt_handler:create_access_token:39 | JWT令牌创建成功，用户: testuser
2025-08-07 10:39:27 | INFO     | app.api.v1.auth:login:67 | 用户登录成功: testuser
2025-08-07 10:40:07 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:40:15 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:40:15 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:40:23 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年8月, 截止日期: None
2025-08-07 10:40:23 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.04秒
2025-08-07 10:40:23 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年8月, 截止日期: None
2025-08-07 10:40:23 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共2条记录
2025-08-07 10:40:33 | INFO     | app.services.report_service:get_monthly_report:52 | 开始实时计算月度报告: 2025年7月, 截止日期: None
2025-08-07 10:40:33 | INFO     | app.services.report_service:get_monthly_report:82 | 月度报告实时计算完成，耗时: 0.01秒
2025-08-07 10:40:39 | INFO     | app.services.report_service:get_monthly_distribution_details:237 | 开始获取月度宿舍分布详情: 2025年7月, 截止日期: None
2025-08-07 10:40:39 | INFO     | app.services.report_service:get_monthly_distribution_details:311 | 月度宿舍分布详情获取完成，共6条记录
2025-08-07 10:40:43 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:40:43 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:40:43 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:40:43 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:40:55 | INFO     | app.services.record_service:delete_record:263 | 删除入住记录成功: 8444591b-6340-4902-96c3-1b2fb854026e
2025-08-07 10:40:55 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:41:05 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 111111 -> 宿舍4
2025-08-07 10:41:05 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:41:23 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:41:23 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:41:23 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:41:23 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:41:40 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:41:45 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:41:53 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:41:53 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:42:04 | INFO     | app.services.record_service:delete_record:263 | 删除入住记录成功: 8093c3ab-0f9e-42b6-af4f-1ba8f61220a6
2025-08-07 10:42:04 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:42:07 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:42:07 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:42:07 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:42:07 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:42:07 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:42:07 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:42:07 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:42:07 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:42:17 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 10:42:23 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 333333 -> 宿舍3
2025-08-07 10:42:23 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:43:03 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:43:14 | INFO     | app.services.department_service:create_department:108 | 创建部门成功: 部门D
2025-08-07 10:43:14 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:43:17 | INFO     | app.services.department_service:delete_department:177 | 删除部门成功: 09a75c0f-2f6b-4b0a-ab04-0ee842a39168
2025-08-07 10:43:44 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:43:54 | INFO     | app.services.department_service:create_department:108 | 创建部门成功: 部门D
2025-08-07 10:43:54 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:43:56 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:43:59 | INFO     | app.services.department_service:delete_department:177 | 删除部门成功: afa16a66-4998-488a-bebf-99c52e1cabd8
2025-08-07 10:44:30 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共3条记录
2025-08-07 10:45:26 | INFO     | app.services.department_service:create_department:108 | 创建部门成功: 部门D
2025-08-07 10:45:27 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:45:37 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:45:39 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:45:52 | INFO     | app.services.dormitory_service:create_dormitory:139 | 创建宿舍成功: 宿舍5
2025-08-07 10:45:52 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共5条记录
2025-08-07 10:45:57 | INFO     | app.services.dormitory_service:delete_dormitory:232 | 删除宿舍成功: efb2561b-35df-4cf9-bb3f-86579c49ecec
2025-08-07 10:45:57 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:45:58 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:45:58 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:46:00 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:46:00 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:46:15 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:46:23 | INFO     | app.services.department_service:create_department:108 | 创建部门成功: 部门E
2025-08-07 10:46:23 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:46:25 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:46:26 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:46:27 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共7条记录
2025-08-07 10:46:42 | INFO     | app.services.resident_service:create_resident:137 | 创建住户成功: 888888
2025-08-07 10:46:42 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:46:49 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:46:49 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:47:13 | INFO     | app.services.resident_service:update_resident:187 | 更新住户成功: 111111
2025-08-07 10:47:13 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:47:16 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:47:18 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:47:20 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:47:20 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:47:20 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:47:20 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:48:02 | INFO     | app.main:shutdown_event:99 | 应用正在关闭...
2025-08-07 10:48:10 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:48:10 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:48:21 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:48:22 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共5条记录
2025-08-07 10:48:28 | INFO     | app.services.department_service:delete_department:177 | 删除部门成功: 129c04ce-6f0c-4fc0-83ea-8939e9a2fa19
2025-08-07 10:48:28 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:48:30 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:49:14 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:49:14 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:49:17 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:49:18 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:49:20 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:49:20 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:49:32 | INFO     | app.services.resident_service:create_resident:137 | 创建住户成功: 999999
2025-08-07 10:49:32 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共9条记录
2025-08-07 10:49:37 | INFO     | app.services.resident_service:delete_resident:210 | 删除住户成功: 999999
2025-08-07 10:49:37 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:49:39 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:49:40 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:49:41 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:49:47 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:49:52 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:49:52 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:49:52 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:49:52 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:49:55 | INFO     | app.services.record_service:delete_record:263 | 删除入住记录成功: 2b5e23b1-0e98-4eee-96e8-a1f592d94662
2025-08-07 10:49:55 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共6条记录
2025-08-07 10:50:06 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 10:50:10 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 555555 -> 宿舍3
2025-08-07 10:50:10 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:52:39 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 10:52:39 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 10:52:43 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:52:43 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:52:43 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:52:43 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:52:47 | INFO     | app.services.record_service:checkout_resident:316 | 办理住户离开成功: 555555
2025-08-07 10:52:47 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:52:53 | INFO     | app.services.record_service:checkout_resident:316 | 办理住户离开成功: 333333
2025-08-07 10:52:53 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 10:56:59 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共1条记录
2025-08-07 10:59:33 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:59:34 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 10:59:34 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 10:59:34 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 10:59:34 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共7条记录
2025-08-07 11:00:01 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 11:00:09 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 666666 -> 宿舍2
2025-08-07 11:00:09 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共8条记录
2025-08-07 11:00:22 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 11:00:28 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 888888 -> 宿舍4
2025-08-07 11:00:28 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共9条记录
2025-08-07 11:00:55 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 11:01:03 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 住户已有活跃的入住记录，请先办理离开手续
2025-08-07 11:01:12 | ERROR    | app.services.record_service:create_record:179 | 创建入住记录失败: 该宿舍没有可用床位
2025-08-07 11:01:20 | INFO     | app.services.record_service:create_record:175 | 创建入住记录成功: 777777 -> 宿舍2
2025-08-07 11:01:20 | INFO     | app.services.record_service:get_records:73 | 获取入住记录列表成功，共10条记录
2025-08-07 11:02:49 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 11:02:49 | INFO     | app.main:startup_event:91 | 数据库表创建成功
2025-08-07 11:02:53 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 11:02:55 | INFO     | app.services.department_service:get_departments:52 | 获取部门列表成功，共4条记录
2025-08-07 11:02:55 | INFO     | app.services.dormitory_service:get_dormitories:64 | 获取宿舍列表成功，共4条记录
2025-08-07 11:02:55 | INFO     | app.services.resident_service:get_residents:65 | 获取住户列表成功，共8条记录
2025-08-07 11:02:55 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共10条记录
2025-08-07 11:02:59 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共10条记录
2025-08-07 11:03:28 | ERROR    | app.services.record_service:create_record:193 | 创建入住记录失败: 该宿舍没有可用床位
2025-08-07 11:03:32 | INFO     | app.services.record_service:create_record:189 | 创建入住记录成功: 555555 -> 宿舍3
2025-08-07 11:03:32 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共10条记录
2025-08-07 11:03:34 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共1条记录
2025-08-07 11:03:36 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共10条记录
2025-08-07 11:03:38 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共11条记录
2025-08-07 11:03:41 | INFO     | app.services.record_service:get_records:81 | 获取入住记录列表成功，共10条记录
2025-08-07 11:11:00 | INFO     | __main__:test_mysql_connection:21 | 开始测试MySQL数据库连接...
2025-08-07 11:11:00 | INFO     | __main__:test_mysql_connection:22 | 数据库连接URL: mysql+pymysql://root:password@localhost:3306/my_database
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:56 | MySQL数据库连接测试失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**********' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:57 | 请检查以下配置:
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:58 | 1. MySQL服务是否已启动
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:59 | 2. 数据库连接参数是否正确
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:60 | 3. 数据库用户权限是否足够
2025-08-07 11:11:00 | ERROR    | __main__:test_mysql_connection:61 | 4. 防火墙是否阻止连接
2025-08-07 11:13:49 | INFO     | app.main:startup_event:86 | 启动 宿舍入住管理系统 v1.0.0
2025-08-07 11:13:49 | ERROR    | app.main:startup_event:93 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**********' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
