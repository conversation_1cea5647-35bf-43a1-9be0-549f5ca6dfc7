# MySQL 迁移指南

本文档详细说明如何将项目从 SQLite 迁移到 MySQL 数据库。

## 📋 迁移概述

本次迁移包含以下主要变更：

1. **数据库驱动**：从 SQLite 切换到 MySQL (使用 PyMySQL 驱动)
2. **连接配置**：更新数据库连接字符串和相关配置
3. **模型兼容性**：确保所有 SQLAlchemy 模型与 MySQL 兼容
4. **初始化脚本**：提供新的数据库初始化和测试脚本

## 🔧 环境准备

### 1. 安装 MySQL 服务器

确保你的系统已安装 MySQL 5.7+ 或 8.0+：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# macOS (使用 Homebrew)
brew install mysql

# Windows
# 下载并安装 MySQL Community Server
```

### 2. 启动 MySQL 服务

```bash
# Linux
sudo systemctl start mysql
sudo systemctl enable mysql

# macOS
brew services start mysql

# Windows
# 通过服务管理器启动 MySQL 服务
```

### 3. 创建数据库用户和数据库

```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE my_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选，也可以使用 root）
CREATE USER 'bedsharing'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON my_database.* TO 'bedsharing'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

## 🚀 迁移步骤

### 方式一：使用 Docker (推荐)

如果你有 Docker 环境，可以快速启动 MySQL 服务：

```bash
# 一键启动 MySQL 服务
python start_mysql.py

# 或者手动启动
docker-compose -f docker-compose.mysql.yml up -d
```

这将启动：
- MySQL 8.0 服务 (端口 3306)
- Adminer 数据库管理界面 (端口 8080)

### 方式二：使用本地 MySQL

### 1. 安装 Python 依赖

```bash
pip install pymysql
```

### 2. 更新环境配置 (两种方式都需要)

编辑 `.env` 文件，更新数据库连接字符串：

```env
# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/my_database
```

**连接字符串格式说明：**
- `mysql+pymysql://` - 使用 PyMySQL 驱动的 MySQL 连接
- `root:password` - 用户名和密码
- `localhost:3306` - 主机和端口
- `my_database` - 数据库名称

### 3. 测试数据库连接

```bash
# 显示连接信息
python test_mysql_connection.py --info

# 创建数据库（如果不存在）
python test_mysql_connection.py --create-db

# 测试连接
python test_mysql_connection.py
```

### 4. 初始化数据库

```bash
# 初始化数据库表和基础数据
python init_mysql_db.py

# 如果需要重置数据库
python init_mysql_db.py --reset
```

### 5. 运行迁移测试

```bash
# 运行完整的迁移测试
python test_mysql_migration.py
```

### 6. 启动应用

```bash
# 启动后端服务
python run.py
```

## 📊 主要变更说明

### 1. 数据库配置变更

**原配置 (SQLite):**
```python
DATABASE_URL = "sqlite:///./dormitory_management.db"
```

**新配置 (MySQL):**
```python
DATABASE_URL = "mysql+pymysql://root:password@localhost:3306/my_database"
```

### 2. 连接引擎优化

添加了 MySQL 特定的连接池配置：

```python
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,      # 连接池预检查
    pool_recycle=3600,       # 连接回收时间（1小时）
)
```

### 3. 模型兼容性

所有现有模型都与 MySQL 兼容，包括：
- ✅ 基础数据类型 (String, Integer, Boolean, Date, DateTime)
- ✅ JSON 字段支持
- ✅ 外键约束
- ✅ 检查约束
- ✅ 唯一约束
- ✅ 索引

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 连接被拒绝
```
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")
```

**解决方案：**
- 确保 MySQL 服务已启动
- 检查主机名和端口是否正确
- 检查防火墙设置

#### 2. 认证失败
```
pymysql.err.OperationalError: (1045, "Access denied for user")
```

**解决方案：**
- 检查用户名和密码是否正确
- 确保用户有足够的权限
- 检查用户是否允许从指定主机连接

#### 3. 数据库不存在
```
pymysql.err.OperationalError: (1049, "Unknown database")
```

**解决方案：**
```bash
# 使用脚本自动创建数据库
python test_mysql_connection.py --create-db
```

#### 4. 字符集问题
```
UnicodeEncodeError: 'latin-1' codec can't encode characters
```

**解决方案：**
确保数据库使用 UTF-8 字符集：
```sql
ALTER DATABASE my_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📈 性能优化建议

### 1. 索引优化
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_residents_employee_id ON residents(employee_id);
CREATE INDEX idx_records_status ON residence_records(status);
CREATE INDEX idx_records_dates ON residence_records(check_in_date, check_out_date);
```

### 2. 连接池配置
```python
# 在生产环境中调整连接池参数
engine = create_engine(
    database_url,
    pool_size=20,           # 连接池大小
    max_overflow=30,        # 最大溢出连接数
    pool_pre_ping=True,     # 连接预检查
    pool_recycle=3600,      # 连接回收时间
)
```

### 3. 查询优化
- 使用适当的索引
- 避免 N+1 查询问题
- 使用连接查询替代多次单表查询
- 合理使用分页

## 🔒 安全建议

1. **不要在生产环境使用 root 用户**
2. **使用强密码**
3. **限制数据库用户权限**
4. **启用 SSL 连接**
5. **定期备份数据**

## 📝 备份和恢复

### 备份数据
```bash
# 备份整个数据库
mysqldump -u root -p my_database > backup.sql

# 备份特定表
mysqldump -u root -p my_database departments dormitories > tables_backup.sql
```

### 恢复数据
```bash
# 恢复数据库
mysql -u root -p my_database < backup.sql
```

## ✅ 验证清单

迁移完成后，请确认以下项目：

- [ ] MySQL 服务正常运行
- [ ] 数据库连接测试通过
- [ ] 所有数据表创建成功
- [ ] 基础数据初始化完成
- [ ] 应用程序正常启动
- [ ] API 接口正常响应
- [ ] 前端页面正常显示
- [ ] 数据查询和更新功能正常

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. 查看应用日志：`logs/app.log`
2. 运行诊断脚本：`python test_mysql_connection.py`
3. 检查 MySQL 错误日志
4. 参考本文档的故障排除部分

---

**迁移完成后，建议保留原 SQLite 数据库文件作为备份，直到确认 MySQL 版本运行稳定。**
