# 应用基本配置
APP_NAME=宿舍入住管理系统
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=wangzhixin

# 服务器配置
HOST=0.0.0.0
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 数据库配置
DATABASE_URL=mysql+pymysql://root:root@localhost:3306/my_database

# CORS配置
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# API配置
API_V1_PREFIX=/api/v1

# 导出配置
EXPORT_MAX_RECORDS=10000
EXPORT_TIMEOUT=300

# 缓存配置
CACHE_TTL=3600
