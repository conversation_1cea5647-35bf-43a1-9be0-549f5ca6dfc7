-- MySQL 数据库初始化脚本
-- 设置字符集和排序规则

-- 确保数据库使用正确的字符集
ALTER DATABASE my_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权（如果不存在）
CREATE USER IF NOT EXISTS 'bedsharing'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON my_database.* TO 'bedsharing'@'%';
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 显示当前数据库信息
SELECT 
    'Database initialized successfully' as message,
    DATABASE() as current_database,
    VERSION() as mysql_version,
    @@character_set_database as charset,
    @@collation_database as collation;
