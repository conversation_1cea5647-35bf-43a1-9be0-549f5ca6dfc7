# 应用基本配置
APP_NAME=宿舍入住管理系统
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-super-secret-key-change-in-production

# 服务器配置
HOST=0.0.0.0
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/my_database
# 开发环境可使用SQLite
# DATABASE_URL=sqlite:///./dormitory_management.db
DATABASE_ECHO=false
MYSQL_DB=my_database
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_PORT=3306

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_PORT=6379

# JWT安全配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# API配置
API_V1_PREFIX=/api/v1

# 业务配置
UNIT_COST_PER_BED_DAY=100.0
DEFAULT_COMPANY_DEPARTMENT=公司

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_EXTENSIONS=.xlsx,.xls,.csv,.pdf

# 导出配置
EXPORT_MAX_RECORDS=10000
EXPORT_TIMEOUT=300

# 缓存配置
CACHE_TTL=3600

# 生产环境配置
DOMAIN_NAME=your-domain.com
HTTP_PORT=80
HTTPS_PORT=443

# 数据库管理工具
ADMINER_PORT=8080

# 监控配置
GRAFANA_PASSWORD=admin

# LDAP配置（可选）
LDAP_SERVER=
LDAP_PORT=389
LDAP_BASE_DN=
LDAP_USER_DN=
LDAP_PASSWORD=

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
