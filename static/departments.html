<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>部门管理 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/layout.css">
  <link rel="stylesheet" href="css/components.css">
</head>
<body>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="app-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div class="logo-mini hidden">🏠</div>
      </div>
      
      <nav class="menu">
        <div class="menu-item">
          <a href="reports.html">
            <span class="menu-icon">📊</span>
            <span class="menu-title">报表统计</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="records.html">
            <span class="menu-icon">📋</span>
            <span class="menu-title">入住记录</span>
          </a>
        </div>
        <div class="menu-item active">
          <a href="departments.html">
            <span class="menu-icon">🏢</span>
            <span class="menu-title">部门管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="dormitories.html">
            <span class="menu-icon">🏠</span>
            <span class="menu-title">宿舍管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="residents.html">
            <span class="menu-icon">👤</span>
            <span class="menu-title">住户管理</span>
          </a>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="app-main-container">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <button class="sidebar-toggle">📁</button>
          <nav class="breadcrumb">
            <div class="breadcrumb-item">
              <a href="reports.html">首页</a>
            </div>
            <div class="breadcrumb-item">
              <a href="departments.html">部门管理</a>
            </div>
          </nav>
        </div>
        
        <div class="header-right">
          <button class="btn refresh-btn" title="刷新页面">🔄</button>
          <div class="user-info">
            <span>👤</span>
            <span class="username">用户</span>
            <span class="arrow-down">▼</span>
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                <span>👤</span>
                个人信息
              </a>
              <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                <span>🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="app-main">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">部门管理</h1>
          <div class="page-actions">
            <button class="btn btn-primary" onclick="showCreateDialog()">
              <span>➕</span>
              新增部门
            </button>
            <button class="btn" onclick="refreshData()">
              <span>🔄</span>
              刷新
            </button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="card">
          <div class="card__body">
            <form class="search-form" id="searchForm" style="display: flex; gap: 16px; align-items: end;">
              <div class="form-item">
                <label>部门名称</label>
                <input type="text" name="name" class="input__inner" placeholder="请输入部门名称">
              </div>
              <div class="form-item">
                <button type="button" class="btn btn-primary" onclick="handleSearch()">
                  <span>🔍</span>
                  搜索
                </button>
                <button type="button" class="btn" onclick="resetSearch()">
                  <span>🔄</span>
                  重置
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
          <div class="card__body">
            <div class="table-container">
              <table class="table" id="departmentsTable">
                <thead>
                  <tr>
                    <th>部门名称</th>
                    <th>描述</th>
                    <th>住户数量</th>
                    <th>创建时间</th>
                    <th>更新时间</th>
                    <th style="width: 200px;">操作</th>
                  </tr>
                </thead>
                <tbody id="departmentsTableBody">
                  <tr>
                    <td colspan="6" style="text-align: center; padding: 40px;">
                      <div class="loading">加载中...</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 移动端遮罩 -->
  <div class="sidebar-overlay"></div>

  <!-- 新增/编辑对话框 -->
  <div class="dialog-overlay" id="departmentDialog" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3 id="dialogTitle">新增部门</h3>
      </div>
      <div class="dialog-body">
        <form class="form" id="departmentForm">
          <div class="form-item">
            <label class="form-item__label">部门名称 *</label>
            <div class="form-item__content">
              <input type="text" name="name" class="input__inner" placeholder="请输入部门名称" maxlength="100" required>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">描述</label>
            <div class="form-item__content">
              <textarea name="description" class="input__inner" placeholder="请输入部门描述" maxlength="500" style="min-height: 80px; resize: vertical;"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button type="button" class="btn" onclick="closeDepartmentDialog()">取消</button>
        <button type="button" class="btn btn-primary" onclick="handleSubmit()" id="submitBtn">创建</button>
      </div>
    </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/layout.js"></script>
  <script src="js/page-transition.js"></script>
  <script>
    // 部门管理逻辑
    class DepartmentsManager {
      constructor() {
        this.departments = [];
        this.loading = false;
        this.currentDepartment = null;
        this.isEdit = false;
        this.searchForm = { name: '' };
        this.init();
      }

      async init() {
        await this.loadDepartments();
      }

      async loadDepartments() {
        this.loading = true;
        this.renderLoading();

        try {
          const params = new URLSearchParams();
          if (this.searchForm.name) {
            params.append('name', this.searchForm.name);
          }

          const response = await api.get(`/v1/departments?${params.toString()}`);
          this.departments = response.items || response;
          this.renderDepartments();
        } catch (error) {
          console.error('加载部门失败:', error);
          this.renderError();
        } finally {
          this.loading = false;
        }
      }

      renderLoading() {
        const tbody = document.getElementById('departmentsTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="6" style="text-align: center; padding: 40px;">
              <div class="loading"></div>
              <div style="margin-top: 12px;">加载中...</div>
            </td>
          </tr>
        `;
      }

      renderDepartments() {
        const tbody = document.getElementById('departmentsTableBody');
        
        if (!this.departments || this.departments.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="6" style="text-align: center; padding: 40px;">
                <div class="empty">
                  <div class="empty-description">暂无部门数据</div>
                </div>
              </td>
            </tr>
          `;
          return;
        }

        tbody.innerHTML = this.departments.map(dept => `
          <tr>
            <td>${dept.name}</td>
            <td>${dept.description || '-'}</td>
            <td>${dept.resident_count || 0}</td>
            <td>${formatDate(dept.created_at, 'YYYY-MM-DD HH:mm')}</td>
            <td>${formatDate(dept.updated_at, 'YYYY-MM-DD HH:mm')}</td>
            <td>
              <div style="display: flex; gap: 8px;">
                <button class="btn btn-primary" onclick="showEditDialog('${dept.id}')" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                <button class="btn btn-danger" onclick="handleDelete('${dept.id}')" style="padding: 4px 8px; font-size: 12px;">删除</button>
              </div>
            </td>
          </tr>
        `).join('');
      }

      renderError() {
        const tbody = document.getElementById('departmentsTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="6" style="text-align: center; padding: 40px;">
              <div class="empty">
                <div class="empty-description">加载失败，请重试</div>
              </div>
            </td>
          </tr>
        `;
      }

      showCreateDialog() {
        this.isEdit = false;
        this.currentDepartment = null;
        document.getElementById('dialogTitle').textContent = '新增部门';
        document.getElementById('submitBtn').textContent = '创建';
        document.getElementById('departmentForm').reset();
        document.getElementById('departmentDialog').style.display = 'flex';
      }

      showEditDialog(departmentId) {
        const department = this.departments.find(d => d.id === departmentId);
        if (!department) {
          showMessage('部门不存在', 'error');
          return;
        }

        this.isEdit = true;
        this.currentDepartment = department;
        document.getElementById('dialogTitle').textContent = '编辑部门';
        document.getElementById('submitBtn').textContent = '更新';

        const form = document.getElementById('departmentForm');
        form.querySelector('input[name="name"]').value = department.name;
        form.querySelector('textarea[name="description"]').value = department.description || '';

        document.getElementById('departmentDialog').style.display = 'flex';
      }

      closeDepartmentDialog() {
        document.getElementById('departmentDialog').style.display = 'none';
        document.getElementById('departmentForm').reset();
        this.currentDepartment = null;
        this.isEdit = false;
      }

      async handleSubmit() {
        const form = document.getElementById('departmentForm');
        const formData = new FormData(form);

        const validator = new FormValidator(form);
        validator.addRule('name', [{ required: true, message: '请输入部门名称' }]);

        const validation = validator.validate();
        if (!validation.valid) return;

        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '提交中...';
        submitBtn.disabled = true;

        try {
          const data = {
            name: formData.get('name'),
            description: formData.get('description') || null
          };

          if (this.isEdit) {
            await api.put(`/v1/departments/${this.currentDepartment.id}`, data);
            showMessage('部门更新成功', 'success');
          } else {
            await api.post('/v1/departments', data);
            showMessage('部门创建成功', 'success');
          }

          this.closeDepartmentDialog();
          await this.loadDepartments();
        } catch (error) {
          console.error('提交失败:', error);
          showMessage(error.message || '提交失败', 'error');
        } finally {
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }

      async handleDelete(departmentId) {
        const department = this.departments.find(d => d.id === departmentId);
        if (!department) {
          showMessage('部门不存在', 'error');
          return;
        }

        const confirmed = await showConfirm(`确定要删除部门"${department.name}"吗？`, '删除确认');
        if (!confirmed) return;

        try {
          await api.delete(`/v1/departments/${departmentId}`);
          showMessage('部门删除成功', 'success');
          await this.loadDepartments();
        } catch (error) {
          console.error('删除失败:', error);
          showMessage(error.message || '删除失败', 'error');
        }
      }

      handleSearch() {
        const form = document.getElementById('searchForm');
        const formData = new FormData(form);
        this.searchForm = { name: formData.get('name') || '' };
        this.loadDepartments();
      }

      resetSearch() {
        document.getElementById('searchForm').reset();
        this.searchForm = { name: '' };
        this.loadDepartments();
      }

      async refreshData() {
        await this.loadDepartments();
        showMessage('数据已刷新', 'success');
      }
    }

    // 全局函数
    function showCreateDialog() { window.departmentsManager?.showCreateDialog(); }
    function showEditDialog(id) { window.departmentsManager?.showEditDialog(id); }
    function closeDepartmentDialog() { window.departmentsManager?.closeDepartmentDialog(); }
    function handleSubmit() { window.departmentsManager?.handleSubmit(); }
    function handleDelete(id) { window.departmentsManager?.handleDelete(id); }
    function handleSearch() { window.departmentsManager?.handleSearch(); }
    function resetSearch() { window.departmentsManager?.resetSearch(); }
    function refreshData() { window.departmentsManager?.refreshData(); }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      window.departmentsManager = new DepartmentsManager();
    });
  </script>
</body>
</html>
