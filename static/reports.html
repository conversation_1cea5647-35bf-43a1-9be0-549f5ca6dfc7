<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>报表统计 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/layout.css">
  <link rel="stylesheet" href="css/components.css">
  <style>
    /* 统计卡片样式 */
    .stats-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: var(--border-radius-base);
      box-shadow: var(--box-shadow-light);
      transition: transform 0.3s;
    }

    .stats-item:hover {
      transform: translateY(-2px);
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-right: 16px;
    }

    .stats-content {
      flex: 1;
    }

    .stats-value {
      font-size: 24px;
      font-weight: 700;
      color: var(--text-color-primary);
      line-height: 1;
      margin-bottom: 4px;
    }

    .stats-label {
      font-size: 14px;
      color: var(--text-color-secondary);
    }

    /* 宿舍卡片样式 */
    .dormitory-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .dormitory-card {
      background: white;
      border-radius: var(--border-radius-base);
      box-shadow: var(--box-shadow-light);
      overflow: hidden;
    }

    .dormitory-card .card-header {
      padding: 16px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border-color-lighter);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .dormitory-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dormitory-icon {
      color: var(--primary-color);
      font-size: 18px;
    }

    .dormitory-name {
      font-weight: 600;
      color: var(--text-color-primary);
    }

    .allocation-badge .tag {
      background: var(--info-color);
      color: white;
      border: none;
    }

    .dormitory-card .card-body {
      padding: 20px;
    }

    .department-usage-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color-regular);
      margin-bottom: 16px;
    }

    .department-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--border-color-extra-light);
    }

    .department-item:last-child {
      border-bottom: none;
    }

    .department-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dept-icon {
      color: var(--text-color-secondary);
      font-size: 16px;
    }

    .dept-name {
      font-weight: 500;
      color: var(--text-color-primary);
    }

    .department-percentage {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: 120px;
    }

    .percentage-value {
      font-weight: 600;
      font-size: 14px;
      min-width: 40px;
      text-align: right;
    }

    .percentage-value.percentage-high { color: var(--success-color); }
    .percentage-value.percentage-medium { color: var(--warning-color); }
    .percentage-value.percentage-low { color: var(--primary-color); }
    .percentage-value.percentage-very-low { color: var(--danger-color); }

    .percentage-bar {
      height: 6px;
      border-radius: 3px;
      transition: width 0.3s ease;
      min-width: 2px;
    }

    .percentage-bar.bar-high { background: var(--success-color); }
    .percentage-bar.bar-medium { background: var(--warning-color); }
    .percentage-bar.bar-low { background: var(--primary-color); }
    .percentage-bar.bar-very-low { background: var(--danger-color); }

    /* 分布详情样式 */
    .dormitory-section {
      margin-bottom: 30px;
      background: white;
      border-radius: var(--border-radius-base);
      overflow: hidden;
      box-shadow: var(--box-shadow-light);
    }

    .dormitory-header {
      padding: 16px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .dormitory-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-primary);
    }

    .dormitory-header .department-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .distribution-table {
      width: 100%;
    }

    .empty-dormitory {
      padding: 40px 20px;
      text-align: center;
      color: var(--text-color-secondary);
    }

    .days-badge {
      background: #ecf5ff;
      color: var(--primary-color);
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    /* 日期选择器样式 */
    .date-input {
      width: 200px;
    }

    /* 响应式 */
    @media (max-width: 768px) {
      .dormitory-cards {
        grid-template-columns: 1fr;
      }
      
      .department-percentage {
        min-width: 100px;
      }
      
      .date-input {
        width: 150px;
      }
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="app-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div class="logo-mini hidden">🏠</div>
      </div>
      
      <nav class="menu">
        <div class="menu-item active">
          <a href="reports.html">
            <span class="menu-icon">📊</span>
            <span class="menu-title">报表统计</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="records.html">
            <span class="menu-icon">📋</span>
            <span class="menu-title">入住记录</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="departments.html">
            <span class="menu-icon">🏢</span>
            <span class="menu-title">部门管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="dormitories.html">
            <span class="menu-icon">🏠</span>
            <span class="menu-title">宿舍管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="residents.html">
            <span class="menu-icon">👤</span>
            <span class="menu-title">住户管理</span>
          </a>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="app-main-container">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <button class="sidebar-toggle">📁</button>
          <nav class="breadcrumb">
            <div class="breadcrumb-item">
              <a href="reports.html">报表统计</a>
            </div>
          </nav>
        </div>
        
        <div class="header-right">
          <button class="btn refresh-btn" title="刷新页面">🔄</button>

          <!-- 用户信息下拉菜单 -->
          <div class="user-info">
            <span>👤</span>
            <span class="username">用户</span>
            <span class="arrow-down">▼</span>
            
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                <span>👤</span>
                个人信息
              </a>
              <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                <span>🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="app-main">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">报表统计</h1>
          <div class="page-actions">
            <button class="btn btn-primary" onclick="refreshRealtimeReport()">
              <span>🔄</span>
              刷新实时数据
            </button>
          </div>
        </div>

        <!-- 实时报告 -->
        <div class="card">
          <div class="card__header">
            <h3 class="card-title">实时报告</h3>
            <button class="btn" onclick="refreshRealtimeReport()">
              <span>🔄</span>
            </button>
          </div>
          
          <div class="card__body">
            <div id="realtimeContent" class="stats-grid">
              <!-- 实时数据将在这里显示 -->
              <div class="skeleton skeleton-title"></div>
              <div class="skeleton skeleton-paragraph"></div>
            </div>
          </div>
        </div>

        <!-- 月度报告 -->
        <div class="card">
          <div class="card__header">
            <h3 class="card-title">月度报告</h3>
            <div class="flex items-center" style="gap: 12px;">
              <input 
                type="month" 
                id="selectedMonth" 
                class="input__inner date-input"
                onchange="fetchMonthlyReport()"
              >
              <button class="btn btn-success" onclick="exportMonthlyReport()" id="exportBtn">
                <span>📥</span>
                导出Excel
              </button>
            </div>
          </div>
          
          <div class="card__body">
            <div id="monthlyContent">
              <div class="empty">
                <div class="empty-description">请选择月份查看报告</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 宿舍部门分布详情 -->
        <div class="card">
          <div class="card__header">
            <h3 class="card-title">宿舍部门分布详情</h3>
            <div class="flex items-center" style="gap: 12px;">
              <input 
                type="month" 
                id="distributionMonth" 
                class="input__inner date-input"
                onchange="fetchDistributionDetails()"
              >
              <button class="btn" onclick="fetchDistributionDetails()" id="refreshDistributionBtn">
                <span>🔄</span>
                刷新
              </button>
            </div>
          </div>
          
          <div class="card__body">
            <div id="distributionContent">
              <div class="empty">
                <div class="empty-description">请选择月份查看宿舍分布详情</div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 移动端遮罩 -->
  <div class="sidebar-overlay"></div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/layout.js"></script>
  <script src="js/page-transition.js"></script>
  <script src="js/reports.js"></script>
</body>
</html>
