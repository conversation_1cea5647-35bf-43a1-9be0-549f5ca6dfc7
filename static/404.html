<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>页面不存在 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 
                   'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 
                   Helvetica, Arial, sans-serif;
    }

    .error-container {
      text-align: center;
      background: rgba(255, 255, 255, 0.95);
      padding: 60px 40px;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      max-width: 500px;
      width: 90%;
    }

    .error-code {
      font-size: 120px;
      font-weight: 700;
      color: var(--primary-color);
      margin: 0;
      line-height: 1;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .error-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-color-primary);
      margin: 20px 0 16px 0;
    }

    .error-description {
      font-size: 16px;
      color: var(--text-color-secondary);
      margin-bottom: 40px;
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .error-actions .btn {
      min-width: 120px;
    }

    .error-icon {
      font-size: 80px;
      margin-bottom: 20px;
      opacity: 0.8;
    }

    /* 背景装饰 */
    .bg-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;
    }

    .bg-shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .shape-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    .shape-4 {
      width: 80px;
      height: 80px;
      top: 30%;
      right: 30%;
      animation-delay: 3s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
      .error-container {
        padding: 40px 20px;
      }
      
      .error-code {
        font-size: 80px;
      }
      
      .error-title {
        font-size: 20px;
      }
      
      .error-description {
        font-size: 14px;
      }
      
      .error-actions {
        flex-direction: column;
        align-items: center;
      }
      
      .error-actions .btn {
        width: 100%;
        max-width: 200px;
      }
    }

    /* 动画效果 */
    .error-container {
      animation: slideIn 0.6s ease-out;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .error-code {
      animation: bounce 2s ease-in-out infinite;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }
  </style>
</head>
<body>
  <div class="bg-shapes">
    <div class="bg-shape shape-1"></div>
    <div class="bg-shape shape-2"></div>
    <div class="bg-shape shape-3"></div>
    <div class="bg-shape shape-4"></div>
  </div>

  <div class="error-container">
    <div class="error-icon">🏠</div>
    <h1 class="error-code">404</h1>
    <h2 class="error-title">页面不存在</h2>
    <p class="error-description">
      抱歉，您访问的页面不存在或已被移除。<br>
      请检查网址是否正确，或返回首页继续浏览。
    </p>
    
    <div class="error-actions">
      <button class="btn btn-primary" onclick="goHome()">
        <span>🏠</span>
        返回首页
      </button>
      <button class="btn" onclick="goBack()">
        <span>⬅️</span>
        返回上页
      </button>
    </div>
  </div>

  <script>
    // 返回首页
    function goHome() {
      window.location.href = 'reports.html';
    }

    // 返回上一页
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        goHome();
      }
    }

    // 键盘事件
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        goHome();
      } else if (e.key === 'Escape') {
        goBack();
      }
    });

    // 自动跳转倒计时（可选）
    let countdown = 10;
    const countdownElement = document.createElement('div');
    countdownElement.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      z-index: 1000;
    `;
    
    function updateCountdown() {
      if (countdown > 0) {
        countdownElement.textContent = `${countdown}秒后自动跳转到首页`;
        countdown--;
        setTimeout(updateCountdown, 1000);
      } else {
        goHome();
      }
    }

    // 可以选择是否启用自动跳转
    // document.body.appendChild(countdownElement);
    // updateCountdown();
  </script>
</body>
</html>
