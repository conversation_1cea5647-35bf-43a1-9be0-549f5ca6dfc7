// 报表统计页面逻辑

class ReportsManager {
  constructor() {
    this.realtimeReport = null;
    this.monthlyReport = null;
    this.distributionDetails = null;
    this.monthlyLoading = false;
    this.distributionLoading = false;
    this.init();
  }

  init() {
    // 设置默认月份为当前月份
    const currentMonth = new Date().toISOString().slice(0, 7);
    document.getElementById('selectedMonth').value = currentMonth;
    document.getElementById('distributionMonth').value = currentMonth;

    // 初始加载数据
    this.refreshRealtimeReport();
    this.fetchMonthlyReport();
    this.fetchDistributionDetails();
  }

  // 获取实时报告
  async refreshRealtimeReport() {
    try {
      const response = await api.get('/v1/reports/realtime');
      this.realtimeReport = response;
      this.renderRealtimeReport();
    } catch (error) {
      console.error('获取实时报告失败:', error);
      this.renderRealtimeError();
    }
  }

  // 渲染实时报告
  renderRealtimeReport() {
    const container = document.getElementById('realtimeContent');
    if (!this.realtimeReport) {
      container.innerHTML = '<div class="empty"><div class="empty-description">暂无数据</div></div>';
      return;
    }

    container.innerHTML = `
      <div class="stats-item">
        <div class="stats-icon" style="background: #ecf5ff; color: #409eff;">🏢</div>
        <div class="stats-content">
          <div class="stats-value">${this.realtimeReport.total_departments || 0}</div>
          <div class="stats-label">总部门数</div>
        </div>
      </div>
      <div class="stats-item">
        <div class="stats-icon" style="background: #f0f9ff; color: #67c23a;">🏠</div>
        <div class="stats-content">
          <div class="stats-value">${this.realtimeReport.total_dormitories || 0}</div>
          <div class="stats-label">总宿舍数</div>
        </div>
      </div>
      <div class="stats-item">
        <div class="stats-icon" style="background: #fdf6ec; color: #e6a23c;">🛏️</div>
        <div class="stats-content">
          <div class="stats-value">${this.realtimeReport.total_beds || 0}</div>
          <div class="stats-label">总床位数</div>
        </div>
      </div>
      <div class="stats-item">
        <div class="stats-icon" style="background: #fef0f0; color: #f56c6c;">📈</div>
        <div class="stats-content">
          <div class="stats-value">${this.realtimeReport.occupancy_rate || 0}%</div>
          <div class="stats-label">入住率</div>
        </div>
      </div>
    `;
  }

  // 渲染实时报告错误
  renderRealtimeError() {
    const container = document.getElementById('realtimeContent');
    container.innerHTML = '<div class="empty"><div class="empty-description">加载失败，请重试</div></div>';
  }

  // 获取月度报告
  async fetchMonthlyReport() {
    const selectedMonth = document.getElementById('selectedMonth').value;
    if (!selectedMonth) return;

    this.monthlyLoading = true;
    this.renderMonthlyLoading();

    try {
      const [year, month] = selectedMonth.split('-');
      const response = await api.get(`/v1/reports/monthly/${year}/${month}`);
      this.monthlyReport = response;
      this.renderMonthlyReport();
    } catch (error) {
      console.error('获取月度报告失败:', error);
      this.renderMonthlyError();
    } finally {
      this.monthlyLoading = false;
    }
  }

  // 渲染月度报告加载状态
  renderMonthlyLoading() {
    const container = document.getElementById('monthlyContent');
    container.innerHTML = `
      <div class="skeleton skeleton-title"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
    `;
  }

  // 渲染月度报告
  renderMonthlyReport() {
    const container = document.getElementById('monthlyContent');
    if (!this.monthlyReport || !this.monthlyReport.dormitory_allocations) {
      container.innerHTML = '<div class="empty"><div class="empty-description">暂无宿舍分摊数据</div></div>';
      return;
    }

    let html = '<h4 style="margin-bottom: 20px; display: flex; align-items: center; gap: 8px;"><span>🏠</span>宿舍分摊占比</h4>';
    html += '<div class="dormitory-cards">';

    this.monthlyReport.dormitory_allocations.forEach(dorm => {
      html += `
        <div class="dormitory-card">
          <div class="card-header">
            <div class="dormitory-info">
              <span class="dormitory-icon">🏠</span>
              <span class="dormitory-name">${dorm.dormitory_name}</span>
            </div>
            <div class="allocation-badge">
              <span class="tag">宿舍</span>
            </div>
          </div>
          <div class="card-body">
            <div class="department-usage-title">
              <span style="margin-right: 4px;">🏢</span>
              部门分摊占比情况
            </div>
            <div class="department-tags">
      `;

      if (dorm.departments && Object.keys(dorm.departments).length > 0) {
        Object.entries(dorm.departments).forEach(([deptId, dept]) => {
          const percentage = dept.percentage_in_dorm || 0;
          const percentageClass = this.getPercentageClass(percentage);
          const barClass = this.getPercentageBarClass(percentage);

          html += `
            <div class="department-item">
              <div class="department-info">
                <span class="dept-icon">👤</span>
                <span class="dept-name">${dept.department_name}</span>
              </div>
              <div class="department-percentage">
                <span class="percentage-value ${percentageClass}">${percentage}%</span>
                <div class="percentage-bar ${barClass}" style="width: ${Math.max(percentage, 2)}%;"></div>
              </div>
            </div>
          `;
        });
      } else {
        html += '<div class="empty"><div class="empty-description">暂无部门数据</div></div>';
      }

      html += `
            </div>
          </div>
        </div>
      `;
    });

    html += '</div>';
    container.innerHTML = html;
  }

  // 渲染月度报告错误
  renderMonthlyError() {
    const container = document.getElementById('monthlyContent');
    container.innerHTML = '<div class="empty"><div class="empty-description">加载失败，请重试</div></div>';
  }

  // 获取宿舍分布详情
  async fetchDistributionDetails() {
    const distributionMonth = document.getElementById('distributionMonth').value;
    if (!distributionMonth) return;

    this.distributionLoading = true;
    this.renderDistributionLoading();

    try {
      const [year, month] = distributionMonth.split('-');
      const response = await api.get(`/v1/reports/monthly/${year}/${month}/distribution`);
      this.distributionDetails = response;
      this.renderDistributionDetails();
    } catch (error) {
      console.error('获取宿舍分布详情失败:', error);
      showMessage('获取宿舍分布详情失败', 'error');
      this.renderDistributionError();
    } finally {
      this.distributionLoading = false;
    }
  }

  // 渲染分布详情加载状态
  renderDistributionLoading() {
    const container = document.getElementById('distributionContent');
    container.innerHTML = `
      <div class="skeleton skeleton-title"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
      <div class="skeleton skeleton-paragraph"></div>
    `;
  }

  // 渲染分布详情
  renderDistributionDetails() {
    const container = document.getElementById('distributionContent');
    if (!this.distributionDetails || !this.distributionDetails.dormitories) {
      container.innerHTML = '<div class="empty"><div class="empty-description">暂无分布数据</div></div>';
      return;
    }

    let html = '<div class="distribution-tables">';

    this.distributionDetails.dormitories.forEach(dormitory => {
      html += `
        <div class="dormitory-section">
          <div class="dormitory-header">
            <h4 class="dormitory-title">
              <span>🏠</span>
              ${dormitory.dormitory_name}
              <span class="tag tag-info">${dormitory.total_beds}床位</span>
              ${dormitory.records.length === 0 ? '<span class="tag tag-warning">空宿舍</span>' : ''}
            </h4>
      `;

      // 部门标签
      if (dormitory.department_summary && Object.keys(dormitory.department_summary).length > 0) {
        html += '<div class="department-tags">';
        Object.entries(dormitory.department_summary).forEach(([deptName, count]) => {
          const tagType = this.getDepartmentTagType(count);
          html += `<span class="tag tag-${tagType}">${deptName}: ${count}人</span>`;
        });
        html += '</div>';
      }

      html += '</div>';

      // 记录表格
      if (dormitory.records.length > 0) {
        html += `
          <table class="table distribution-table">
            <thead>
              <tr>
                <th style="width: 80px; text-align: center;">床位号</th>
                <th style="width: 100px;">姓名</th>
                <th style="width: 120px;">部门</th>
                <th style="width: 120px;">项目组</th>
                <th style="width: 110px; text-align: center;">入住日期</th>
                <th style="width: 110px; text-align: center;">离开日期</th>
                <th style="width: 80px; text-align: center;">状态</th>
                <th style="width: 90px; text-align: center;">本月天数</th>
              </tr>
            </thead>
            <tbody>
        `;

        dormitory.records.forEach(record => {
          const statusText = this.getStatusText(record.status);
          const statusClass = record.status === 'ACTIVE' ? 'success' : 
                             record.status === 'COMPLETED' ? 'info' : 'danger';

          html += `
            <tr>
              <td style="text-align: center;">${record.bed_number}</td>
              <td>${record.resident_name}</td>
              <td>${record.department_name}</td>
              <td>${record.project_group || '-'}</td>
              <td style="text-align: center;">${record.check_in_date}</td>
              <td style="text-align: center;">
                ${record.check_out_date ? record.check_out_date : '<span class="tag tag-success">至今</span>'}
              </td>
              <td style="text-align: center;">
                <span class="tag tag-${statusClass}">${statusText}</span>
              </td>
              <td style="text-align: center;">
                <span class="days-badge">${record.days_in_month}天</span>
              </td>
            </tr>
          `;
        });

        html += '</tbody></table>';
      } else {
        html += '<div class="empty-dormitory"><div class="empty"><div class="empty-description">该宿舍暂无入住记录</div></div></div>';
      }

      html += '</div>';
    });

    html += '</div>';
    container.innerHTML = html;
  }

  // 渲染分布详情错误
  renderDistributionError() {
    const container = document.getElementById('distributionContent');
    container.innerHTML = '<div class="empty"><div class="empty-description">加载失败，请重试</div></div>';
  }

  // 导出月度报告
  async exportMonthlyReport() {
    const selectedMonth = document.getElementById('selectedMonth').value;
    if (!selectedMonth) {
      showMessage('请先选择月份', 'warning');
      return;
    }

    const exportBtn = document.getElementById('exportBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<span class="loading"></span> 导出中...';
    exportBtn.disabled = true;

    try {
      const [year, month] = selectedMonth.split('-');
      const response = await fetch(`${api.baseURL}/v1/reports/monthly/${year}/${month}/export?format=excel`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${api.getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `月度报告_${selectedMonth}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      showMessage('导出成功', 'success');
    } catch (error) {
      console.error('导出失败:', error);
      showMessage('导出失败', 'error');
    } finally {
      exportBtn.innerHTML = originalText;
      exportBtn.disabled = false;
    }
  }

  // 辅助方法
  getPercentageClass(percentage) {
    if (percentage >= 70) return 'percentage-high';
    if (percentage >= 40) return 'percentage-medium';
    if (percentage >= 20) return 'percentage-low';
    return 'percentage-very-low';
  }

  getPercentageBarClass(percentage) {
    if (percentage >= 70) return 'bar-high';
    if (percentage >= 40) return 'bar-medium';
    if (percentage >= 20) return 'bar-low';
    return 'bar-very-low';
  }

  getDepartmentTagType(count) {
    if (count >= 3) return 'success';
    if (count >= 2) return 'warning';
    if (count >= 1) return 'info';
    return 'danger';
  }

  getStatusText(status) {
    const statusMap = {
      'ACTIVE': '在住',
      'COMPLETED': '已离开',
      'CANCELLED': '已取消'
    };
    return statusMap[status] || status;
  }
}

// 全局函数
function refreshRealtimeReport() {
  if (window.reportsManager) {
    window.reportsManager.refreshRealtimeReport();
  }
}

function fetchMonthlyReport() {
  if (window.reportsManager) {
    window.reportsManager.fetchMonthlyReport();
  }
}

function fetchDistributionDetails() {
  if (window.reportsManager) {
    window.reportsManager.fetchDistributionDetails();
  }
}

function exportMonthlyReport() {
  if (window.reportsManager) {
    window.reportsManager.exportMonthlyReport();
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.reportsManager = new ReportsManager();
});
