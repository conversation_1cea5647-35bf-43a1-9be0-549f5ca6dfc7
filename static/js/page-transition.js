// 页面切换管理器

class PageTransitionManager {
  constructor() {
    this.isTransitioning = false;
    this.loadingBar = null;
    this.init();
  }

  init() {
    this.createLoadingBar();
    this.setupPageTransitions();
    this.setupLinkInterception();
    this.animatePageLoad();
  }

  // 创建加载进度条
  createLoadingBar() {
    this.loadingBar = document.createElement('div');
    this.loadingBar.className = 'page-loading';
    document.body.appendChild(this.loadingBar);
  }

  // 显示加载进度条
  showLoadingBar() {
    if (this.loadingBar) {
      this.loadingBar.classList.add('show');
    }
  }

  // 隐藏加载进度条
  hideLoadingBar() {
    if (this.loadingBar) {
      this.loadingBar.classList.remove('show');
    }
  }

  // 设置页面切换动画
  setupPageTransitions() {
    // 为主内容区域添加过渡类
    const mainContent = document.querySelector('.app-main');
    if (mainContent) {
      mainContent.classList.add('page-transition');
      // 延迟添加show类以触发动画
      setTimeout(() => {
        mainContent.classList.add('show');
      }, 50);
    }
  }

  // 拦截链接点击，添加过渡效果
  setupLinkInterception() {
    // 拦截菜单链接
    const menuLinks = document.querySelectorAll('.menu-item a');
    menuLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const href = link.getAttribute('href');
        if (href && href !== '#') {
          this.navigateWithTransition(href);
        }
      });
    });

    // 拦截面包屑链接
    const breadcrumbLinks = document.querySelectorAll('.breadcrumb-item a');
    breadcrumbLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const href = link.getAttribute('href');
        if (href && href !== '#') {
          this.navigateWithTransition(href);
        }
      });
    });

    // 拦截页面操作按钮（更安全的方式）
    document.addEventListener('click', (e) => {
      const target = e.target.closest('a[href$=".html"]');
      if (target && !target.hasAttribute('target')) {
        e.preventDefault();
        const href = target.getAttribute('href');
        if (href && href !== window.location.pathname) {
          this.navigateWithTransition(href);
        }
      }
    });
  }

  // 带过渡效果的页面导航
  async navigateWithTransition(href) {
    if (this.isTransitioning) return;

    this.isTransitioning = true;

    try {
      // 显示加载条
      this.showLoadingBar();

      // 滚动到顶部
      this.scrollToTop(200);

      // 添加退出动画
      const mainContent = document.querySelector('.app-main');
      if (mainContent) {
        mainContent.style.opacity = '0';
        mainContent.style.transform = 'translateY(-20px)';
      }

      // 等待退出动画完成
      await this.delay(300);

      // 导航到新页面
      window.location.href = href;
    } catch (error) {
      console.error('页面切换失败:', error);
      this.isTransitioning = false;
      this.hideLoadingBar();
    }
  }

  // 页面加载动画
  animatePageLoad() {
    // 隐藏加载条
    setTimeout(() => {
      this.hideLoadingBar();
    }, 500);

    // 为卡片添加交错动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
    });

    // 为统计项添加交错动画
    const statsItems = document.querySelectorAll('.stats-item');
    statsItems.forEach((item, index) => {
      item.style.animationDelay = `${index * 0.1}s`;
      item.style.animation = 'slideInUp 0.6s ease-out both';
    });

    // 为表格行添加动画
    this.animateTableRows();
  }

  // 表格行动画
  animateTableRows() {
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach((row, index) => {
      if (index < 10) { // 只为前10行添加动画，避免性能问题
        row.style.animationDelay = `${index * 0.05}s`;
      }
    });
  }

  // 工具方法：延迟
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 平滑滚动到顶部
  scrollToTop(duration = 300) {
    const start = window.pageYOffset;
    const startTime = performance.now();

    const animateScroll = (currentTime) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      
      // 使用缓动函数
      const easeInOutQuad = progress < 0.5 
        ? 2 * progress * progress 
        : -1 + (4 - 2 * progress) * progress;
      
      window.scrollTo(0, start * (1 - easeInOutQuad));
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }

  // 添加悬停效果
  addHoverEffects() {
    // 为按钮添加悬停效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      });

      button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = '';
      });
    });

    // 为卡片添加悬停效果
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-4px)';
        card.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0)';
        card.style.boxShadow = '';
      });
    });
  }

  // 添加点击波纹效果
  addRippleEffect() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s linear;
          pointer-events: none;
        `;
        
        button.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });

    // 添加波纹动画CSS
    if (!document.querySelector('#ripple-styles')) {
      const style = document.createElement('style');
      style.id = 'ripple-styles';
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }

  // 优化表格渲染性能
  optimizeTableRendering() {
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
      // 使用虚拟滚动优化大表格
      const tbody = table.querySelector('tbody');
      if (tbody && tbody.children.length > 50) {
        this.implementVirtualScrolling(table);
      }
    });
  }

  // 实现虚拟滚动（简化版）
  implementVirtualScrolling(table) {
    // 这里可以实现虚拟滚动逻辑
    // 为了简化，我们只是添加一个滚动优化
    const tbody = table.querySelector('tbody');
    tbody.style.transform = 'translateZ(0)'; // 启用硬件加速
  }
}

// 页面性能优化
class PagePerformanceOptimizer {
  constructor() {
    this.init();
  }

  init() {
    this.optimizeImages();
    this.preloadCriticalResources();
    this.setupIntersectionObserver();
  }

  // 图片懒加载优化
  optimizeImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }

  // 预加载关键资源
  preloadCriticalResources() {
    const criticalPages = ['reports.html', 'records.html'];
    criticalPages.forEach(page => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = page;
      document.head.appendChild(link);
    });
  }

  // 设置交叉观察器优化动画
  setupIntersectionObserver() {
    const animateOnScroll = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    // 观察需要动画的元素
    const animateElements = document.querySelectorAll('.card, .stats-item');
    animateElements.forEach(el => animateOnScroll.observe(el));
  }
}

// 全局初始化
let pageTransitionManager;
let performanceOptimizer;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  pageTransitionManager = new PageTransitionManager();
  performanceOptimizer = new PagePerformanceOptimizer();
  
  // 添加额外的交互效果
  setTimeout(() => {
    pageTransitionManager.addHoverEffects();
    pageTransitionManager.addRippleEffect();
    pageTransitionManager.optimizeTableRendering();
  }, 100);
});

// 页面显示时的处理（浏览器前进后退）
window.addEventListener('pageshow', (e) => {
  if (e.persisted) {
    // 页面从缓存中恢复，重新初始化动画
    if (pageTransitionManager) {
      pageTransitionManager.animatePageLoad();
    }
  }
});

// 导出到全局
window.PageTransitionManager = PageTransitionManager;
window.PagePerformanceOptimizer = PagePerformanceOptimizer;
