<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户登录 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/components.css">
  <style>
    .login-container {
      position: relative;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    .login-box {
      position: relative;
      z-index: 10;
      width: 400px;
      padding: 40px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .login-header .logo {
      margin-bottom: 16px;
      font-size: 40px;
      color: var(--primary-color);
    }

    .login-header h2 {
      color: var(--text-color-primary);
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .login-header p {
      color: var(--text-color-secondary);
      font-size: 14px;
      margin: 0;
    }

    .login-form .form-item {
      margin-bottom: 20px;
    }

    .login-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
      font-weight: 500;
    }

    .login-footer {
      margin-top: 20px;
      text-align: center;
    }

    .demo-accounts p {
      color: var(--text-color-secondary);
      font-size: 12px;
      margin: 0 0 8px 0;
    }

    .demo-accounts .tag {
      cursor: pointer;
      margin-right: 8px;
      font-size: 12px;
    }

    .demo-accounts .tag:hover {
      opacity: 0.8;
    }

    /* 背景装饰 */
    .login-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1;
    }

    .bg-shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .shape-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
      .login-box {
        width: 90%;
        padding: 30px 20px;
      }
      
      .login-header h2 {
        font-size: 20px;
      }
    }

    /* 输入框图标 */
    .input-with-icon {
      position: relative;
    }

    .input-with-icon .input__inner {
      padding-left: 40px;
    }

    .input-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-color-placeholder);
      font-size: 16px;
    }

    .password-toggle {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-color-placeholder);
      cursor: pointer;
      font-size: 16px;
    }

    .password-toggle:hover {
      color: var(--text-color-regular);
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">🏠</div>
        <h2>宿舍入住管理系统</h2>
        <p>请输入您的账号和密码登录</p>
      </div>

      <form class="login-form form" id="loginForm">
        <div class="form-item">
          <div class="input input-large input-with-icon">
            <span class="input-icon">👤</span>
            <input 
              type="text" 
              name="username" 
              class="input__inner" 
              placeholder="请输入用户名"
              autocomplete="username"
            >
          </div>
        </div>

        <div class="form-item">
          <div class="input input-large input-with-icon">
            <span class="input-icon">🔒</span>
            <input 
              type="password" 
              name="password" 
              class="input__inner" 
              placeholder="请输入密码"
              autocomplete="current-password"
            >
            <span class="password-toggle" onclick="togglePassword()">👁️</span>
          </div>
        </div>

        <div class="form-item">
          <button type="submit" class="btn btn-primary btn-large login-button">
            <span class="btn-text">登录</span>
            <span class="loading hidden">加载中...</span>
          </button>
        </div>
      </form>

      <div class="login-footer">
        <div class="demo-accounts">
          <p>测试账号：</p>
          <span class="tag tag-primary" onclick="fillDemoAccount('testuser')">
            testuser / testpass
          </span>
          <span class="tag tag-success" onclick="fillDemoAccount('admin')">
            admin / admin123
          </span>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="login-bg">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/page-transition.js"></script>
  <script>
    // 表单验证器
    const validator = new FormValidator(document.getElementById('loginForm'));
    
    // 添加验证规则
    validator.addRule('username', [
      { required: true, message: '请输入用户名' },
      { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符' }
    ]);
    
    validator.addRule('password', [
      { required: true, message: '请输入密码' },
      { min: 1, message: '密码不能为空' }
    ]);

    // 表单提交处理
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      // 表单验证
      const validation = validator.validate();
      if (!validation.valid) {
        return;
      }

      const formData = new FormData(e.target);
      const loginData = {
        username: formData.get('username').trim(),
        password: formData.get('password')
      };

      // 显示加载状态
      setLoginLoading(true);

      try {
        await authManager.login(loginData);
        // 登录成功，跳转到首页
        window.location.href = 'reports.html';
      } catch (error) {
        console.error('登录失败:', error);
      } finally {
        setLoginLoading(false);
      }
    });

    // 设置登录按钮加载状态
    function setLoginLoading(loading) {
      const button = document.querySelector('.login-button');
      const btnText = button.querySelector('.btn-text');
      const loadingText = button.querySelector('.loading');
      
      if (loading) {
        button.disabled = true;
        btnText.classList.add('hidden');
        loadingText.classList.remove('hidden');
      } else {
        button.disabled = false;
        btnText.classList.remove('hidden');
        loadingText.classList.add('hidden');
      }
    }

    // 切换密码显示
    function togglePassword() {
      const passwordInput = document.querySelector('input[name="password"]');
      const toggle = document.querySelector('.password-toggle');
      
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggle.textContent = '🙈';
      } else {
        passwordInput.type = 'password';
        toggle.textContent = '👁️';
      }
    }

    // 填充演示账号
    function fillDemoAccount(type) {
      const usernameInput = document.querySelector('input[name="username"]');
      const passwordInput = document.querySelector('input[name="password"]');
      
      if (type === 'testuser') {
        usernameInput.value = 'testuser';
        passwordInput.value = 'testpass';
      } else if (type === 'admin') {
        usernameInput.value = 'admin';
        passwordInput.value = 'admin123';
      }
      
      // 清除验证错误
      validator.clearErrors();
    }

    // 回车键登录
    document.addEventListener('keyup', (e) => {
      if (e.key === 'Enter') {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
      }
    });
  </script>
</body>
</html>
