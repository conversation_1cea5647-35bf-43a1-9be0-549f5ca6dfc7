# 宿舍入住管理系统 - 纯HTML版本

这是从Vue3项目迁移而来的纯HTML + CSS + JavaScript版本，无需构建工具，可直接在浏览器中运行。

## 项目结构

```
static/
├── css/                    # 样式文件
│   ├── common.css         # 通用样式（替代Element Plus）
│   ├── layout.css         # 布局样式
│   └── components.css     # 组件样式
├── js/                    # JavaScript文件
│   ├── utils.js          # 工具函数和API客户端
│   ├── auth.js           # 认证管理
│   ├── layout.js         # 布局管理
│   ├── page-transition.js # 页面切换优化
│   └── reports.js        # 报表页面逻辑
├── login.html            # 登录页面
├── reports.html          # 报表统计页面（首页）
├── records.html          # 入住记录页面
├── departments.html      # 部门管理页面
├── dormitories.html      # 宿舍管理页面
├── residents.html        # 住户管理页面
├── 404.html             # 404错误页面
├── test-transitions.html # 页面切换测试页面
└── README.md            # 说明文档
```

## 功能特性

### ✅ 已实现功能

1. **用户认证**
   - 登录/登出
   - Token管理
   - 路由守卫

2. **报表统计**
   - 实时数据展示
   - 月度报告
   - 宿舍分布详情
   - Excel导出

3. **入住记录管理**
   - 记录列表查看
   - 新增/编辑记录
   - 办理离开
   - 搜索筛选

4. **部门管理**
   - 部门列表
   - 新增/编辑/删除部门
   - 搜索功能

5. **宿舍管理**
   - 宿舍列表
   - 新增/编辑/删除宿舍
   - 床位管理
   - 入住率显示

6. **住户管理**
   - 住户列表
   - 新增/编辑/删除住户
   - 部门关联
   - 搜索功能

7. **UI组件**
   - 响应式布局
   - 侧边栏导航
   - 表格组件
   - 表单验证
   - 消息提示
   - 确认对话框
   - 加载动画

8. **页面切换优化** ⭐ 新增
   - 流畅的页面过渡动画
   - 加载进度条
   - 硬件加速优化
   - 平滑滚动效果
   - 交错动画
   - 性能优化

## 使用方法

### 1. 直接运行
无需安装任何依赖，直接双击HTML文件即可在浏览器中打开：

```bash
# 打开登录页面
双击 login.html

# 或者打开测试页面
双击 test.html
```

### 2. 本地服务器运行（推荐）
为了避免跨域问题，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx serve .

# 使用PHP
php -S localhost:8000
```

然后访问：http://localhost:8000/login.html

### 3. 测试功能
访问 `test.html` 页面可以测试所有功能组件。

## API配置

系统默认API地址为 `/api`，可以在 `js/utils.js` 中修改：

```javascript
const api = new ApiClient('/api'); // 修改为你的API地址
```

## 登录测试

系统提供了测试账号：
- 用户名：`testuser` 密码：`testpass`
- 用户名：`admin` 密码：`admin123`

## 浏览器兼容性

支持现代浏览器：
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 技术栈

- **HTML5**: 语义化标签
- **CSS3**: Flexbox、Grid、动画、硬件加速
- **JavaScript ES6+**: 类、模块、异步/等待
- **Fetch API**: HTTP请求
- **LocalStorage**: 本地存储
- **页面切换优化**: 流畅动画、性能优化

## 迁移说明

### 从Vue3迁移的主要变化：

1. **组件系统** → **原生JavaScript类**
   - Vue组件 → JavaScript类
   - 响应式数据 → 手动DOM更新
   - 生命周期 → 事件监听

2. **路由系统** → **多页面应用**
   - Vue Router → 直接页面跳转
   - 路由守卫 → 页面加载时检查

3. **状态管理** → **本地存储**
   - Pinia Store → LocalStorage + 内存状态
   - 响应式更新 → 手动状态同步

4. **UI框架** → **自定义样式**
   - Element Plus → 自定义CSS组件
   - 图标 → Unicode表情符号

5. **构建工具** → **无构建**
   - Vite → 直接运行
   - 模块打包 → 脚本标签引入

## 开发建议

1. **添加新页面**：
   - 复制现有页面HTML结构
   - 创建对应的JavaScript逻辑
   - 更新导航菜单

2. **添加新功能**：
   - 在对应的Manager类中添加方法
   - 更新HTML模板
   - 添加事件监听

3. **样式定制**：
   - 修改CSS变量来改变主题色
   - 在`common.css`中添加通用样式
   - 在页面中添加特定样式

## 注意事项

1. **API调用**：确保后端API支持CORS
2. **文件路径**：使用相对路径引用资源
3. **浏览器缓存**：开发时可能需要强制刷新
4. **错误处理**：检查浏览器控制台的错误信息

## 故障排除

### 常见问题：

1. **页面空白**：检查JavaScript控制台错误
2. **API调用失败**：检查网络面板和CORS设置
3. **样式异常**：检查CSS文件是否正确加载
4. **功能无响应**：检查事件监听是否正确绑定

### 调试方法：

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求状态
4. 使用Elements面板检查DOM结构

## 更新日志

- **v1.0.0**: 完成Vue3到纯HTML的完整迁移
  - 实现所有核心功能
  - 保持原有UI设计
  - 添加响应式布局
  - 完成功能测试

## 许可证

本项目遵循原Vue3项目的许可证。
